// Import video type directly to avoid circular references
export interface Video {
  id: string;
  songId: string;
  artist?: string;
  title: string;
  description: string;
  duration?: number;
  url: string;
  thumbnail: string;
  provider: string;
  videoStatus?: VideoStatus;
}

export type PowerHourID = string;

export type VideoStatus = {
  videoId: string;
  status: string;
  timeVerified?: number;
};

export interface PowerHour {
  id: PowerHourID;
  title: string;
  search: string;
  description: string;
  videos: Video[];
  songs: Song[];
  entries: PowerHourEntry[];
  mostSimilarGenre: MusicGenre;
  genres: MusicGenre[];
  upvotes?: number;
  downvotes?: number;
  feedback?: {
    reportedIssues: string[];
  }
  currentStep?: PowerHourGenerationSteps;
  stepProgress?: number;
  lastUpdateTime?: number;
  error?: string;
  createdAt?: number;
  updatedAt?: number;
}

export interface PowerHourEntry {
  id: string;
  title?: string;

  songId: string;
  videoId: string;

  song: Song;
  video: Video;
  backup_video?: Video; // Additional field for Firestore data structure

  idealStartTime: number;
  idealEndTime: number;

  videoStatus: VideoStatus;

  stats: {
    upvotes?: number;
    downvotes?: number;
    views?: number;
    comments?: number;
  };
  reportedIssues?: string[];

  isReplacement?: boolean;
  markedForReplacement?: boolean;
}

export interface Song {
  id: string;
  title: string;
  artist: string;
  genre: MusicGenre | string; 
  album?: string; 
  year?: number; 
  durationInSeconds?: number;
  idealStartTime?: number;
  idealEndTime?: number;
  thumbnailUrl?: string; 

  isReplacement?: boolean;
  markedForReplacement?: boolean;
}

export enum MusicGenre {
  Pop = "Pop",
  Rock = "Rock",
  HipHop = "Hip-Hop/Rap",
  Jazz = "Jazz",
  Blues = "Blues",
  Country = "Country",
  Electronic = "Electronic/Dance",
  Classical = "Classical",
  Reggae = "Reggae",
  RnB = "R&B (Rhythm and Blues)",
  Folk = "Folk",
  Punk = "Punk",
  Metal = "Metal",
  Soul = "Soul",
  Funk = "Funk",
  Gospel = "Gospel",
  Ska = "Ska",
  Indie = "Indie",
  Alternative = "Alternative",
  Latin = "Latin",
  World = "World",
  Ambient = "Ambient",
  Reggaeton = "Reggaeton",
  Techno = "Techno",
  Disco = "Disco",
  House = "House",
  Dubstep = "Dubstep",
  Rap = "Rap",
  EDM = "EDM (Electronic Dance Music)",
  KPop = "K-Pop",
  JPop = "J-Pop",
  ClassicalCrossover = "Classical Crossover",
  NewWave = "New Wave",
  Psychedelic = "Psychedelic",
  Grunge = "Grunge",
  HardRock = "Hard Rock",
  Experimental = "Experimental",
  Acoustic = "Acoustic",
  BluesRock = "Blues Rock",
  PopPunk = "Pop Punk",
  Bluegrass = "Bluegrass",
  Cajun = "Cajun",
  Flamenco = "Flamenco",
  Salsa = "Salsa",
  Afrobeat = "Afrobeat",
  Soca = "Soca",
  Tejano = "Tejano",
  Zydeco = "Zydeco",
  Kizomba = "Kizomba",
  AvantGarde = "Avant-Garde"
}

export enum PowerHourGenerationSteps {
  None = "None",
  GenerateSongList = "GenerateSongList",
  ReplaceSongs = "ReplaceSongs",
  SongListToVideos = "SongListToVideos",
  buildPowerHour = "BuildPowerHour",
  Complete = "Complete",
  Error = "Error"
}