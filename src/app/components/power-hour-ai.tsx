"use client";

import React, { useState, useEffect } from "react";
import {
  PowerHour,
  PowerHourEntry,
  PowerHourID,
  Song,
} from "../../models/power-hour";
import PowerHourGallery from "./power-hour-gallery";
import Search from "./search";
import { PHOperations, PHCreation } from "../../models/enums";
import * as RTDB from "../providers/power-hour";
import { N8nProvider } from "../providers/n8n-provider";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";
import { PHActions } from "../../../functions/src/[models]/enums";
import ClientOnly from "./utils/client-only";

export default function PowerHourAI() {
  const [search, setSearch] = useState("");
  const [powerHour, setPowerHour] = useState<PowerHour>({} as PowerHour);
  const [count, setCount] = useState(5);

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    console.log("reading query params");
    const powerHourId = searchParams.get("powerHourId");
    if (
      powerHourId &&
      powerHourId !== "" &&
      !powerHour.title &&
      !powerHour.songs &&
      !powerHour.entries
    ) {
      console.log("ID in QP, Fetching power hour: ", powerHourId);
      RTDB.fetchPowerHour(powerHour.id).then((pH: PowerHour | null) => {
        if (pH) {
          setPowerHour(pH);
        }
      });
    }
  }, []);

  // Function to handle search and navigation
  const handleSearch = async (searchTerm: string) => {
    if (searchTerm && searchTerm.length !== 0) {
      toast.info("Creating your power hour...");
      console.log("Search Submitted: ", searchTerm, count);

      try {
        console.log("Sending request to n8n...");
        // Send the search term and count to n8n and get back the power hour ID
        const result = await N8nProvider.createPowerHour(searchTerm, count);
        console.log("n8n request completed successfully", result);

        // Check if the result contains an error
        if (result?.error) {
          console.error("n8n returned an error:", result.error);
          toast.error(`Error: ${result.error.error || "Failed to create power hour"}`);
          return;
        }

        if (result?.powerHourId) {
          console.log("Navigating to creation page with ID:", result.powerHourId);
          // Navigate to the power hour page with the ID returned from n8n
          router.push(
            "/power-hour-ai/create-power-hour?powerHourId=" + result.powerHourId
          );
        } else {
          console.error("No power hour ID returned from n8n:", result);
          toast.error("Error: No power hour ID returned from n8n");
        }
      } catch (error) {
        console.error("Error sending request to n8n:", error);
        toast.error("Failed to create power hour. Please try again.");
      }
    }
  };

  // Effect to handle search changes - removed to prevent automatic search on every change
  useEffect(() => {
    // Only process search if coming from a URL parameter
    const searchParam = searchParams.get("search");
    if (searchParam && searchParam !== search) {
      setSearch(searchParam);
      handleSearch(searchParam);
    }
  }, [searchParams]);

  // Function to handle search submission
  const onSearchSubmit = (searchTerm: string) => {
    setSearch(searchTerm);
    handleSearch(searchTerm);
  };

  // Function to handle power hour card clicks
  const handlePowerHourClick = (powerHourId: PowerHourID) => {
    router.push(`/power-hour-ai/power-hour-details?powerHourId=${powerHourId}`);
  };

  return (
    <ClientOnly fallback={<div className="h-full w-full flex items-center justify-center text-white">Loading...</div>}>
      <div className="min-h-screen bg-gray-900 text-white">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-900 py-12">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Power Hour AI
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-8">
                Create the perfect power hour playlist with AI
              </p>
            </div>

            {/* Search Section */}
            <div className="max-w-2xl mx-auto">
              <Search
                search={search}
                setSearch={setSearch}
                count={count}
                setCount={setCount}
                onSearchSubmit={onSearchSubmit}
              />
            </div>
          </div>
        </div>

        {/* Gallery Section */}
        <div className="container mx-auto px-4 py-8">
          <PowerHourGallery
            headerTitle="Browse Power Hours"
            showHeader={true}
            showFirestoreStatus={true}
            onPowerHourClick={handlePowerHourClick}
          />
        </div>
      </div>
    </ClientOnly>
  );
}
