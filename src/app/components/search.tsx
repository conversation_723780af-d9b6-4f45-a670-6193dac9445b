"use client";

import React, { useState } from "react";

interface SearchProps {
  search: string;
  setSearch: (search: string) => void;
  count: number;
  setCount: (count: number) => void;
  onSearchSubmit?: (searchTerm: string) => void;
}

const Search: React.FC<SearchProps> = ({
  search,
  setSearch,
  count,
  setCount,
  onSearchSubmit,
}) => {
  const [searchValue, setSearchValue] = useState(search);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
  };

  // Handler for the Generate button click
  const handleGenerateClick = () => {
    // If onSearchSubmit is provided, use it; otherwise just update the search state
    if (onSearchSubmit && typeof onSearchSubmit === "function") {
      onSearchSubmit(searchValue);
    } else {
      setSearch(searchValue);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      handleGenerateClick();
    }
  };

  return (
    <div className="w-full">
      {/* Search Input */}
      <div className="flex items-center w-full max-w-2xl mx-auto mb-6">
        <input
          type="text"
          placeholder="Describe your perfect power hour..."
          className="form-input w-full px-4 py-3 border rounded-l-lg shadow-sm text-gray-900 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          value={searchValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
        />
        <button
          className={`px-6 py-3 rounded-r-lg transition duration-150 font-medium ${
            searchValue
              ? "bg-purple-600 hover:bg-purple-700 text-white"
              : "bg-gray-400 text-gray-200 cursor-not-allowed"
          }`}
          onClick={handleGenerateClick}
          disabled={!searchValue}
        >
          Generate
        </button>
      </div>

      {/* Count Selection */}
      <div className="w-full max-w-md mx-auto mb-8">
        <div className="flex flex-col items-center">
          <div className="text-white mb-3 font-medium">
            Number of songs:
          </div>
          <div className="flex flex-wrap justify-center gap-3">
            {[2, 5, 10, 30, 60].map((value) => (
              <button
                key={value}
                onClick={() => setCount(value)}
                className={`px-4 py-2 rounded-lg transition duration-150 font-medium ${
                  count === value
                    ? "bg-purple-600 text-white"
                    : "bg-gray-700 text-white border border-gray-600 hover:bg-gray-600"
                }`}
              >
                {value}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Example Cards */}
      <div className="flex flex-wrap justify-center items-stretch gap-4 w-full max-w-4xl mx-auto">
        {/* Example Card 1 */}
        <div
          onClick={() => setSearchValue("Top songs from each EDM sub genre")}
          className="flex flex-col items-center justify-between bg-gray-800 border border-gray-600 rounded-lg shadow-md p-4 w-full sm:w-1/3 cursor-pointer hover:bg-gray-700 transition-colors duration-300"
        >
          <h3 className="text-lg font-semibold mb-2 text-white">Be Creative</h3>
          <p className="text-gray-300 text-center">{'"Top songs from each EDM sub genre"'}</p>
        </div>

        {/* Example Card 2 */}
        <div
          onClick={() => setSearchValue("Only Taylor Swift")}
          className="flex flex-col items-center justify-between bg-gray-800 border border-gray-600 rounded-lg shadow-md p-4 w-full sm:w-1/3 cursor-pointer hover:bg-gray-700 transition-colors duration-300"
        >
          <h3 className="text-lg font-semibold mb-2 text-white">By Artist</h3>
          <p className="text-gray-300 text-center">{'"Only Taylor Swift"'}</p>
        </div>

        {/* Example Card 3 */}
        <div
          onClick={() => setSearchValue("Grunge of the 90s")}
          className="flex flex-col items-center justify-between bg-gray-800 border border-gray-600 rounded-lg shadow-md p-4 w-full sm:w-1/3 cursor-pointer hover:bg-gray-700 transition-colors duration-300"
        >
          <h3 className="text-lg font-semibold mb-2 text-white">From an Era</h3>
          <p className="text-gray-300 text-center">{'"Grunge of the 90s"'}</p>
        </div>
      </div>
    </div>
  );
};

export default Search;