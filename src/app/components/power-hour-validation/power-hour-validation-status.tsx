"use client";

import React from "react";
import { PowerHour } from "../../../models/power-hour";
import { validatePowerHour, getValidationSummary } from "../../../utils/power-hour-validation";

interface PowerHourValidationStatusProps {
  powerHour: PowerHour | null | undefined;
  showDetails?: boolean;
  className?: string;
}

export default function PowerHourValidationStatus({
  powerHour,
  showDetails = false,
  className = "",
}: PowerHourValidationStatusProps) {
  const validation = validatePowerHour(powerHour);
  const summary = getValidationSummary(validation);

  if (!powerHour) {
    return (
      <div className={`text-red-500 ${className}`}>
        <span className="font-medium">❌ No power hour data</span>
      </div>
    );
  }

  const getStatusIcon = () => {
    if (validation.canPlay) {
      return validation.warnings.length > 0 ? "⚠️" : "✅";
    }
    return "❌";
  };

  const getStatusColor = () => {
    if (validation.canPlay) {
      return validation.warnings.length > 0 ? "text-yellow-600" : "text-green-600";
    }
    return "text-red-600";
  };

  return (
    <div className={`${getStatusColor()} ${className}`}>
      <div className="flex items-center gap-2">
        <span className="text-lg">{getStatusIcon()}</span>
        <span className="font-medium">{summary}</span>
      </div>

      {showDetails && (
        <div className="mt-2 text-sm space-y-1">
          {/* Show validation details */}
          <div className="text-gray-600">
            <span className="font-medium">Valid entries:</span> {validation.validEntryCount} / {validation.totalEntryCount}
          </div>

          {/* Show errors */}
          {validation.errors.length > 0 && (
            <div className="text-red-600">
              <div className="font-medium">Errors:</div>
              <ul className="list-disc list-inside ml-2">
                {validation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Show warnings */}
          {validation.warnings.length > 0 && (
            <div className="text-yellow-600">
              <div className="font-medium">Warnings:</div>
              <ul className="list-disc list-inside ml-2">
                {validation.warnings.slice(0, 5).map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
                {validation.warnings.length > 5 && (
                  <li>... and {validation.warnings.length - 5} more warnings</li>
                )}
              </ul>
            </div>
          )}

          {/* Show missing requirements */}
          {validation.missingRequirements.length > 0 && (
            <div className="text-red-600">
              <div className="font-medium">Missing requirements:</div>
              <ul className="list-disc list-inside ml-2">
                {validation.missingRequirements.map((requirement, index) => (
                  <li key={index}>{requirement}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Simple inline validation badge component
 */
export function PowerHourValidationBadge({
  powerHour,
  className = "",
}: {
  powerHour: PowerHour | null | undefined;
  className?: string;
}) {
  const validation = validatePowerHour(powerHour);

  if (!powerHour) {
    return <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 ${className}`}>
      Invalid
    </span>;
  }

  if (validation.canPlay) {
    const bgColor = validation.warnings.length > 0 ? "bg-yellow-100 text-yellow-800" : "bg-green-100 text-green-800";
    const text = validation.warnings.length > 0 ? "Ready (warnings)" : "Ready";
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${bgColor} ${className}`}>
        {text}
      </span>
    );
  }

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 ${className}`}>
      Cannot play
    </span>
  );
}
