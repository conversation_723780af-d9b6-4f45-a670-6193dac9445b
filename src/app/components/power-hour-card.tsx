import React from "react";
import { PowerHour } from "../../models/power-hour";
import {
	FaMusic,
	FaVideo,
	FaList,
	FaArrowUp,
	FaArrowDown,
	FaThumbsUp,
	FaThumbsDown,
	FaClock,
	FaCalendarAlt,
	FaTags,
} from "react-icons/fa"; // Example icons
import useFeedback from "../providers/use-feedback";
import { CircularProgress } from "@nextui-org/react";
import Image from "next/image";

const PowerHourCard = ({
	powerHour,
	onClick,
}: {
	powerHour: PowerHour;
	onClick: () => void;
}) => {
	// No longer using the feedback hook to simplify compatibility with Firestore
	const loading = false;

	// Helper function to format date
	const formatDate = (timestamp?: number) => {
		if (!timestamp) return null;
		const date = new Date(timestamp);
		return date.toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
			year: 'numeric'
		});
	};

	// Helper function to get entry count
	const getEntryCount = () => {
		if (Array.isArray(powerHour.entries) && powerHour.entries.length > 0) {
			return powerHour.entries.length;
		}
		if (Array.isArray(powerHour.songs) && powerHour.songs.length > 0) {
			return powerHour.songs.length;
		}
		return 0;
	};

	// Helper function to get thumbnail source (prioritize entries over videos)
	const getThumbnails = () => {
		// First try to get thumbnails from entries (which have backup_video data)
		if (Array.isArray(powerHour.entries) && powerHour.entries.length > 0) {
			const thumbnails = [];
			for (const entry of powerHour.entries) {
				// Try backup_video first (from Firestore structure), then video
				const thumbnail = entry.backup_video?.thumbnail || entry.video?.thumbnail;
				if (thumbnail) {
					thumbnails.push(thumbnail);
				}
				if (thumbnails.length >= 9) break;
			}
			return thumbnails;
		}
		// Fallback to videos array
		if (Array.isArray(powerHour.videos) && powerHour.videos.length > 0) {
			return powerHour.videos
				.filter(video => video?.thumbnail)
				.map(video => video.thumbnail)
				.slice(0, 9);
		}
		return [];
	};

	const thumbnails = getThumbnails();
	const entryCount = getEntryCount();
	const createdDate = formatDate(powerHour.createdAt);

	return (
		<div
			className="flex flex-col h-full justify-between rounded-lg overflow-hidden shadow-lg bg-gray-900 text-white cursor-pointer min-h-72 sm:min-h-80 hover:shadow-xl transition-all duration-300 border border-gray-700 hover:border-gray-600 hover:scale-105 touch-manipulation"
			onClick={onClick}
		>
			{/* Header with title and metadata */}
			<div className="p-3 sm:p-4 bg-gray-800">
				<div className="font-bold text-base sm:text-lg text-center line-clamp-2 mb-2">
					{powerHour.title || "Untitled Power Hour"}
				</div>

				{/* Genre and creation date */}
				<div className="space-y-1">
					{powerHour.mostSimilarGenre && (
						<div className="flex items-center justify-center text-xs sm:text-sm text-gray-400">
							<FaTags className="mr-1 text-xs" />
							<span className="truncate">{powerHour.mostSimilarGenre}</span>
						</div>
					)}
					{createdDate && (
						<div className="flex items-center justify-center text-xs text-gray-500">
							<FaCalendarAlt className="mr-1" />
							<span>{createdDate}</span>
						</div>
					)}
				</div>

				{/* Description preview */}
				{powerHour.description && (
					<div className="mt-2 text-xs text-gray-400 text-center line-clamp-2">
						{powerHour.description}
					</div>
				)}
			</div>

			{/* Thumbnails container */}
			<div className="flex-1 relative min-h-32">
				{thumbnails.length > 0 ? (
					<div className="h-full grid grid-cols-3 grid-rows-3 gap-0">
						{thumbnails.map((thumbnail, index) => (
							<div key={index} className="relative">
								<img
									src={thumbnail || '/placeholder-thumbnail.jpg'}
									alt={`Thumbnail ${index + 1}`}
									className="object-cover h-full w-full"
									onError={(e) => {
										// Fallback for broken images
										e.currentTarget.src = '/placeholder-thumbnail.jpg';
									}}
								/>
							</div>
						))}
						{/* Fill remaining slots if less than 9 thumbnails */}
						{Array.from({ length: Math.max(0, 9 - thumbnails.length) }).map((_, index) => (
							<div key={`empty-${index}`} className="bg-gray-700 flex items-center justify-center">
								<FaMusic className="text-gray-500 text-xs" />
							</div>
						))}
					</div>
				) : (
					<div className="h-full flex flex-col items-center justify-center bg-gray-700 text-gray-400">
						<FaMusic className="text-4xl mb-2" />
						<p className="text-sm">No Thumbnails</p>
					</div>
				)}
			</div>

			{/* Enhanced Stats footer */}
			<div className="bg-gray-800 p-2 sm:p-3">
				{/* Primary stats row */}
				<div className="grid grid-cols-3 gap-1 sm:gap-2 text-xs mb-2">
					<div className="flex items-center justify-center">
						<FaMusic className="mr-1" />
						<span className="hidden sm:inline">{entryCount} songs</span>
						<span className="sm:hidden">{entryCount}</span>
					</div>
					<div className="flex items-center justify-center">
						<FaThumbsUp className="mr-1 text-green-400" />
						<span>{powerHour.upvotes || 0}</span>
					</div>
					<div className="flex items-center justify-center">
						<FaThumbsDown className="mr-1 text-red-400" />
						<span>{powerHour.downvotes || 0}</span>
					</div>
				</div>

				{/* Additional metadata row - hidden on mobile for space */}
				<div className="hidden sm:flex justify-center items-center text-xs text-gray-400 space-x-4">
					{powerHour.genres && powerHour.genres.length > 1 && (
						<div className="flex items-center">
							<FaTags className="mr-1" />
							<span>{powerHour.genres.length} genres</span>
						</div>
					)}
					{powerHour.lastUpdateTime && (
						<div className="flex items-center">
							<FaClock className="mr-1" />
							<span>Updated {formatDate(powerHour.lastUpdateTime)}</span>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default PowerHourCard;
