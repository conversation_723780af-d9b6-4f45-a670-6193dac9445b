"use client";

import { PowerHour, PowerHourID } from "@/models/power-hour";
import React, { useEffect, useState } from "react";
import PowerHourGallery from "./power-hour-gallery";
import Search from "./search";
import { useRouter } from "next/navigation";
import { FaSync } from "react-icons/fa";

const Landing = ({
  search,
  setSearch,
  count,
  setCount,
  onSearchSubmit,
}: {
  search: string;
  setSearch: any;
  count: number;
  setCount: any;
  onSearchSubmit?: (searchTerm: string) => void;
}) => {
  const router = useRouter();

  // Function to handle power hour card clicks
  const handlePowerHourClick = (powerHourId: PowerHourID) => {
    router.push(`/power-hour-ai/power-hour-details?powerHourId=${powerHourId}`);
  };



  return (
    <div className="flex flex-col h-screen overflow-auto">
      {/* Hero Section with Search */}
      <section className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-800 via-gray-900 to-black text-white py-8">
        <div className="w-full max-w-6xl mx-auto flex flex-col items-center justify-center px-4">
          <div className="text-5xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            Power Hour AI
          </div>
          <p className="text-xl md:text-2xl text-gray-300 mb-12 text-center">
            Create the perfect power hour playlist with AI
          </p>

          {/* Search Component */}
          <Search
            search={search}
            setSearch={setSearch}
            count={count}
            setCount={setCount}
            onSearchSubmit={onSearchSubmit}
          />
        </div>
      </section>

      {/* Gallery Section */}
      <section className="min-h-screen w-full flex flex-col items-center bg-gray-700 py-12">
        <div className="w-full max-w-6xl mx-auto px-4">
          <PowerHourGallery
            headerTitle="All Power Hours"
            showHeader={true}
            showFirestoreStatus={true}
            onPowerHourClick={handlePowerHourClick}
          />
        </div>
      </section>
    </div>
  );
};

export default Landing;
