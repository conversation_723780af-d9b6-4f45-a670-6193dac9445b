"use client";
import { useState, useEffect, useMemo } from "react";
import { toast } from "react-toastify";
import { motion, AnimatePresence } from "framer-motion";
import { useList } from "@uidotdev/usehooks";
import { useRouter, useSearchParams } from "next/navigation";

// Providers
import { ActiveVideoProvider } from "../../providers/active-video-provider";
import { useCasting } from "../../providers/casting-manager";
import useFeedback from "@/app/providers/use-feedback";

// Models
import { PowerHour, PowerHourEntry } from "../../../models/power-hour";
import { fetchPowerHour } from "../../providers/power-hour";

// Components
import LoadingState from "./loading-state";
import ErrorState from "./error-state";
import PrePlaybackScreen from "./pre-playback-screen";
import PHCountdown from "./ph-countdown";
import PowerHourHeader from "./power-hour-header";
import VideoPlayerSection from "./video-player-section";
import SongInfoCard from "./song-info-card";
import ControlSection from "./control-section";

// Declare JSX namespace to fix 'JSX element implicitly has type any' errors
declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}


export default function ActivePowerHour() {
  const router = useRouter();
  const searchParams = useSearchParams();
  // Use the casting hooks
  const {
    isConnected,
    isCasting,
    castMedia,
    castTab,
    castDesktop,
    castingMode: activeCastingMode,
    stopCasting,
    selectedDevice,
  } = useCasting();

  const [powerHourId, setPowerHourId] = useState("");
  const [powerHour, setPowerHour] = useState<PowerHour | null>(null);
  const [powerHourReady, setPowerHourReady] = useState(false);
  const [powerHourEnded, setPowerHourEnded] = useState(false);
  const [completedEntries, setCompletedEntries] = useState<PowerHourEntry[]>(
    []
  );
  const [currentIndex, setCurrentIndex] = useState(0);
  const [playbackStarted, setPlaybackStarted] = useState(false);
  const [castingMode, setCastingMode] = useState(false);

  const [list, { set, push, removeAt, insertAt, updateAt, clear }] = useList(
    powerHour?.entries ?? []
  );

  const { failedToPlay } = useFeedback(powerHourId);

  /*
		CHANGE POWER HOUR ENDED TO BE A VARIABLE THAT IS SET EVERY RENDER INSTEAD OF USING STATE.I.E. JUST INITAILIZE IT AT THE  TOP
	*/

  const canGoNext = currentIndex < list?.length;
  const canGoPrevious = currentIndex > 0;

  // Helper function to extract YouTube video ID from URL
  const extractYoutubeId = (url: string): string | null => {
    if (!url) return null;
    // Match standard YouTube URLs
    const match = url.match(
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/i
    );
    return match ? match[1] : null;
  };

  const currentEntry = useMemo(() => {
    // Add more debugging to help diagnose issues
    console.debug("Current entry calculation:", {
      powerHourReady,
      currentIndex,
      listLength: list?.length,
      firstEntry: list?.length > 0 ? list[0] : null,
    });

    if (powerHourReady && currentIndex >= list?.length) {
      console.warn("Power hour ended - currentIndex beyond list length");
      setPowerHourEnded(true);
      return null;
    }

    if (list?.length > 0 && currentIndex < list.length) {
      const entry = list[currentIndex];
      console.debug("Processing entry:", entry);

      // Handle case where entry might be malformed
      if (!entry) {
        console.error("Null or undefined entry at index", currentIndex);
        toast.error("This power hour contains invalid data");
        return null;
      }

      // If entry already has videoId property, use it
      if (entry.videoId) {
        return entry;
      }

      // Try to extract video ID from backup_video first, then video
      let videoUrl = null;
      if (entry.backup_video && entry.backup_video.id) {
        videoUrl = entry.backup_video.id;
      } else if (entry.video && entry.video.id) {
        videoUrl = entry.video.id;
      }

      if (videoUrl) {
        const extractedId = extractYoutubeId(videoUrl);
        if (extractedId) {
          // Create a new entry with the extracted ID
          const normalizedEntry = {
            ...entry,
            videoId: extractedId,
          };
          console.debug(
            "Normalized entry with extracted videoId:",
            extractedId,
            "from URL:",
            videoUrl
          );
          // Update the entry in the list for future reference
          updateAt(currentIndex, normalizedEntry);
          return normalizedEntry;
        } else {
          console.warn("Could not extract YouTube ID from URL:", videoUrl);
        }
      }

      console.error("Invalid entry data at index", currentIndex, entry);
      toast.error("This power hour contains invalid data");
      return entry; // Return original entry to avoid null reference errors
    }

    return null; // Explicit return for when no conditions are met
  }, [currentIndex, list, powerHourReady, updateAt]);

  useEffect(() => {
    const powerHourId = searchParams.get("powerHourId");
    if (powerHourId && powerHourId !== "") {
      setPowerHourId(powerHourId);
      setCurrentIndex(parseInt(searchParams.get("currentIndex") ?? "0"));
    } else {
      console.warn("No power hour id found in URL parameters");
      toast.error("No Power Hour ID was provided. Redirecting to home page.");
      // Redirect to homepage after a short delay
      setTimeout(() => {
        router.push("/");
      }, 2000);
    }
  }, [router]);

  useEffect(() => {
    if (powerHourId === "" || !powerHourId) {
      console.warn("No power hour id found");
      return;
    }

    console.log(`Attempting to fetch power hour with ID: ${powerHourId}`);

    // Show loading toast
    const loadingToastId = toast.info("Loading your power hour...");

    fetchPowerHour(powerHourId)
      .then((powerHour: PowerHour | null) => {
        // Close the loading toast

        if (!powerHour) {
          console.error(
            "No power hour found - null return from fetchPowerHour"
          );
          toast.error(
            "Could not find this power hour. It may have been deleted."
          );
          return;
        }
        const safePowerHour = powerHour!;

        console.log("Power hour fetch result:", {
          success: !!safePowerHour,
          hasEntries: (safePowerHour.entries?.length ?? 0) > 0,
          title: safePowerHour?.title,
          entryCount: safePowerHour.entries?.length ?? 0,
        });

        if (
          !safePowerHour.entries ||
          !Array.isArray(safePowerHour.entries) ||
          safePowerHour.entries.length === 0
        ) {
          console.error("Power hour has no entries", safePowerHour);
          toast.error("This power hour doesn't have any songs to play.");
          return;
        }

        // Validate entries have required video data (either video.id or backup_video.id)
        const invalidEntries = (safePowerHour.entries ?? []).filter(
          (entry) => !entry.videoId && !entry.video?.id && !entry.backup_video?.id
        );
        if (invalidEntries.length > 0) {
          console.warn("Some entries are missing video data", invalidEntries);
          toast.warning(
            `${invalidEntries.length} song(s) have invalid video data and will be skipped.`
          );
        }

        // Log entry structure for debugging
        if (safePowerHour.entries && safePowerHour.entries.length > 0) {
          console.log("Sample entry structure:", {
            hasVideoId: !!safePowerHour.entries[0].videoId,
            hasVideoField: !!safePowerHour.entries[0].video,
            hasBackupVideoField: !!safePowerHour.entries[0].backup_video,
            videoId: safePowerHour.entries[0].video?.id,
            backupVideoId: safePowerHour.entries[0].backup_video?.id,
          });
        }

        // Check if this is a test/mock power hour by looking for common test video IDs
        const knownTestVideoIds = ["dQw4w9WgXcQ", "L_jWHffIx5E", "09R8_2nJtjg"];
        const isTestData = (safePowerHour.entries ?? []).some((entry) =>
          knownTestVideoIds.includes(entry.videoId)
        );

        if (isTestData) {
          console.warn("Warning: Loaded test power hour data");
          toast.warning(
            "Your real power hour couldn't be loaded. Please try again or create a new one."
          );
        }

        // Filter out invalid entries before setting (entries with video data)
        const validEntries = (safePowerHour.entries ?? []).filter(
          (entry) => entry.videoId || entry.video?.id || entry.backup_video?.id
        );

        // Update state with the power hour data
        setPowerHour(safePowerHour);
        set(validEntries);
        setPowerHourReady(true);

        // Success message
        toast.success(
          `Loaded ${validEntries.length} songs from "${
            safePowerHour.title || "Untitled Power Hour"
          }"`
        );
      })
      .catch((error) => {
        // Close the loading toast

        console.error("Error fetching power hour:", error);
        toast.error(
          "Failed to load power hour: " + (error?.message || "Unknown error")
        );
      });
  }, [powerHourId]);

  // Implement hooks for managing video state with the casting provider

  useEffect(() => {
    if (powerHourEnded) {
      powerHourComplete();
    }
  }, [powerHourEnded]);

  const handleNextSong = () => {
    window.history.replaceState(
      null,
      "",
      "/power-hour-ai/active-power-hour?powerHourId=" +
        powerHourId +
        "&currentIndex=" +
        (currentIndex + 1)
    );
    setCurrentIndex((i: number) => i + 1);
  };

  const handlePreviousSong = () => {
    setCurrentIndex((i: number) => Math.max(0, i - 1));
  };

  const onSongEnd = (dueToError: boolean = false) => {
    if (!dueToError && currentEntry) {
      // If we're casting media and there's a next song, update the cast media
      if (isCasting && activeCastingMode === "chromecast" && canGoNext) {
        const nextIndex = currentIndex + 1;
        if (nextIndex < list.length) {
          const nextEntry = list[nextIndex];
          if (nextEntry && nextEntry.videoId) {
            // Cast the next video
            castMedia({
              title: `${powerHour?.title || "Power Hour"}: Song ${
                nextIndex + 1
              }`,
              url: `https://www.youtube.com/watch?v=${nextEntry.videoId}`,
              mimeType: "video/mp4",
              startTime: nextEntry.idealStartTime || 0,
              endTime: nextEntry.idealEndTime || 60,
            });
          }
        }
      }
      setCompletedEntries((x: PowerHourEntry[]) => {
        return currentEntry ? [...x, currentEntry] : x;
      });
    } else {
      console.log("Song ended due to error or no current entry");
    }

    if (currentIndex < (list?.length || 0) - 1) {
      // If casting, update the next video in the casting session
      if (
        castingMode &&
        isConnected &&
        isCasting &&
        list &&
        list[currentIndex + 1]
      ) {
        const nextEntry = list[currentIndex + 1];
        const videoUrl = `https://www.youtube.com/watch?v=${nextEntry.videoId}`;
        castMedia({
          title: nextEntry.song?.title || "Power Hour Video",
          url: videoUrl,
          mimeType: "video/mp4",
        });
        toast.info(`Casting: ${nextEntry.song?.title || "Next video"}`);
      }

      handleNextSong();
    } else {
      // End casting when the power hour is complete
      if (castingMode && isConnected && isCasting) {
        stopCasting();
        toast.success("Casting ended - Power Hour complete!");
      }

      powerHourComplete();
    }
  };

  const onSongError = () => {
    failedToPlay(powerHourId, currentEntry?.id ?? "");
    onSongEnd(true);
  };

  const powerHourComplete = () => {
    router.push(
      "/power-hour-ai/active-power-hour-completed?powerHourId=" + powerHourId
    );
  };

  const handleStartPlayback = () => {
    setPlaybackStarted(true);
    setCastingMode(false);
  };

  const handleStartCasting = (castingType?: "tab" | "desktop") => {
    setPlaybackStarted(true);

    if (castingType === "tab") {
      // Start tab casting
      castTab();
      setCastingMode(true);
      toast.success(
        "Now casting the current tab. Controls remain on this device."
      );
      return;
    } else if (castingType === "desktop") {
      // Start desktop casting
      castDesktop();
      setCastingMode(true);
      toast.success(
        "Now casting your desktop. Controls remain on this device."
      );
      return;
    }

    // Regular media casting
    if (currentEntry && selectedDevice) {
      setCastingMode(true);

      // Cast the current video with the new MediaOptions interface
      castMedia({
        title: `${powerHour?.title || "Power Hour"}: Song ${currentIndex + 1}`,
        url: `https://www.youtube.com/watch?v=${currentEntry.videoId}`,
        mimeType: "video/mp4",
        startTime: currentEntry.idealStartTime || 0,
        endTime: currentEntry.idealEndTime || 60,
      });

      toast.success(
        `Now casting to ${selectedDevice.name}. Controls will appear on your device.`
      );
    } else if (!selectedDevice) {
      toast.error("No casting device selected");
    } else {
      toast.error("No video loaded to cast");
    }
  };

  console.log("Render state:", {
    powerHourReady,
    playbackStarted,
    powerHourId,
    currentIndex,
  });

  // Determine the current UI state
  const showLoadingState = !powerHourReady && powerHourId;
  const showPlaybackStart =
    powerHourReady && !playbackStarted && !powerHourEnded;
  const showPowerHour = powerHourReady && playbackStarted && !powerHourEnded;
  const showErrorState = !powerHourId || (powerHourReady && list?.length === 0);

  return (
    <>
      {showLoadingState && <LoadingState />}

      {showErrorState && (
        <ErrorState 
          hasId={!!powerHourId} 
        />
      )}

      {showPlaybackStart && (
        <PrePlaybackScreen
          powerHour={powerHour}
          onStartPlayback={handleStartPlayback}
          onStartCasting={() => handleStartCasting()}
          onStartTabCasting={() => handleStartCasting("tab")}
          onStartDesktopCasting={() => handleStartCasting("desktop")}
        />
      )}

      {showPowerHour && (
        <ActiveVideoProvider
          canPlay={powerHourReady && playbackStarted && !powerHourEnded}
          videoStartTime={currentEntry?.idealStartTime ?? 0}
          videoEndTime={currentEntry?.idealEndTime ?? 60}
        >
          <PHCountdown autoStart={true} castingMode={castingMode} />

          <div className="container mx-auto flex flex-col h-[100vh] w-full overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="flex flex-col flex-grow px-4 md:px-6 py-4 relative h-full overflow-y-auto"
              >
                {/* Power Hour Title */}
                {powerHour && (
                  <PowerHourHeader
                    title={powerHour.title || "Power Hour"}
                    currentIndex={currentIndex}
                    totalSongs={powerHour.entries?.length || 0}
                    castingMode={castingMode}
                  />
                )}

                {/* YouTube Player */}
                {currentEntry && currentEntry.videoId && (
                  <VideoPlayerSection
                    videoId={currentEntry.videoId}
                    startTime={currentEntry.idealStartTime ?? 0}
                    endTime={currentEntry.idealEndTime ?? 60}
                    onSongEnd={onSongEnd}
                    onSongError={onSongError}
                    castingMode={castingMode}
                  />
                )}

                {/* Song Info */}
                {currentEntry && currentEntry.song && (
                  <SongInfoCard
                    title={currentEntry.song.title || "Unknown Song"}
                    artist={currentEntry.song.artist || "Unknown Artist"}
                    year={currentEntry.song.year}
                  />
                )}
              </motion.div>
            </AnimatePresence>

            {/* Controls Section */}
            <ControlSection
              canGoPrevious={canGoPrevious}
              canGoNext={canGoNext}
              onPreviousClick={handlePreviousSong}
              onNextClick={handleNextSong}
              videoId={currentEntry?.videoId || ""}
              castingMode={castingMode}
              currentIndex={currentIndex}
              totalEntries={powerHour?.entries?.length}
              powerHourId={powerHour?.id}
              entryId={currentEntry?.id}
            />
          </div>
        </ActiveVideoProvider>
      )}
    </>
  );
}
