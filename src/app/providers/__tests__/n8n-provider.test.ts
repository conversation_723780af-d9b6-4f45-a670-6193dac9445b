import { toast } from 'react-toastify';
import { N8nProvider } from '../n8n-provider';

// Mock fetch globally
global.fetch = jest.fn();

// Mock the toast notifications
jest.mock('react-toastify');

describe('N8n Provider Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the fetch mock
    (global.fetch as jest.Mock).mockReset();
  });

  describe('createPowerHour', () => {
    it('should create a power hour successfully', async () => {
      // Mock a successful response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ powerHourId: 'new-power-hour-id' }),
      });

      const result = await N8nProvider.createPowerHour('Test Search Term');

      // Verify fetch was called with correct params
      expect(global.fetch).toHaveBeenCalledWith(
        'https://n8n-pve.ensoq.ddns.net/webhook/createPowerHour',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            search: 'Test Search Term',
            count: 60,
            action: 'generate_power_hour',
          }),
        }
      );

      // Verify successful toast notification
      expect(toast.success).toHaveBeenCalledWith('Power hour creation started successfully');

      // Verify correct result
      expect(result).toEqual({ powerHourId: 'new-power-hour-id' });
    });

    it('should create a power hour with custom count', async () => {
      // Mock a successful response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ powerHourId: 'new-power-hour-id' }),
      });

      const result = await N8nProvider.createPowerHour('Test Search Term', 30);

      // Verify fetch was called with correct params including count
      expect(global.fetch).toHaveBeenCalledWith(
        'https://n8n-pve.ensoq.ddns.net/webhook/createPowerHour',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            search: 'Test Search Term',
            count: 30,
            action: 'generate_power_hour',
          }),
        }
      );

      // Verify correct result
      expect(result).toEqual({ powerHourId: 'new-power-hour-id' });
    });

    it('should handle missing powerHourId in response', async () => {
      // Mock a response with missing powerHourId
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ someOtherData: 'value' }),
      });

      const result = await N8nProvider.createPowerHour('Test Search Term');

      // Verify warning toast notification
      expect(toast.warning).toHaveBeenCalledWith('Power hour was created but ID was not returned');

      // Verify fallback result
      expect(result).toEqual({ powerHourId: null });
    });

    it('should handle API error response', async () => {
      // Mock an error response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({
          error: 'Internal server error',
          message: 'Something went wrong',
          timestamp: '2025-05-14T10:56:38-07:00',
        }),
      });

      const result = await N8nProvider.createPowerHour('Test Search Term');

      // Verify error result structure (no toast expected since errors are handled by components)
      expect(result).toHaveProperty('error');
      expect(result.error).toHaveProperty('error', 'Internal server error');
    });

    it('should handle network errors', async () => {
      // Mock a network error
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failure'));

      const result = await N8nProvider.createPowerHour('Test Search Term');

      // Verify error result structure (no toast expected since errors are handled by components)
      expect(result).toHaveProperty('error');
      expect(result.error).toHaveProperty('error', 'Network failure');
      expect(result.error).toHaveProperty('errorCode', 'NETWORK_ERROR');
    });
  });
});
