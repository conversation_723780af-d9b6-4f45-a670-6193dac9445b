import { PowerHour, PowerHourEntry } from "../models/power-hour";
import { 
  PowerHourValidationResult, 
  EntryValidationResult, 
  ValidationOptions 
} from "../types/validation";

/**
 * Validates a single power hour entry to ensure it has the necessary information to play
 * Requirements:
 * - Entry must have an ID
 * - Entry must have song data with name and artist
 * - Entry must have a video link (videoId, video.url, or backup_video.link)
 */
export function validatePowerHourEntry(
  entry: PowerHourEntry, 
  powerHourId?: string,
  options: ValidationOptions = {}
): EntryValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const entryId = entry.id || "unknown";

  // Check for entry ID
  if (!entry.id) {
    errors.push("Entry is missing an ID");
  }

  // Check for song data
  if (!entry.song) {
    errors.push("Entry is missing song data");
  } else {
    // Check song title
    if (!entry.song.title && !options.allowMissingTitle) {
      errors.push("Song is missing a title");
    } else if (!entry.song.title) {
      warnings.push("Song is missing a title");
    }

    // Check song artist
    if (!entry.song.artist && !options.allowMissingArtist) {
      errors.push("Song is missing an artist");
    } else if (!entry.song.artist) {
      warnings.push("Song is missing an artist");
    }
  }

  // Check for video data - multiple possible sources
  const hasVideoId = Boolean(entry.videoId);
  const hasVideoUrl = Boolean(entry.video?.url);
  const hasBackupVideoLink = Boolean(entry.backup_video?.link);
  const hasBackupVideoId = Boolean(entry.backup_video?.id);

  if (!hasVideoId && !hasVideoUrl && !hasBackupVideoLink && !hasBackupVideoId) {
    errors.push("Entry is missing video data (no videoId, video.url, or backup_video.link/id found)");
  } else if (options.strictVideoValidation) {
    // In strict mode, prefer videoId or backup_video.id
    if (!hasVideoId && !hasBackupVideoId) {
      warnings.push("Entry has video data but no direct video ID");
    }
  }

  // Check power hour connection
  if (powerHourId && entry.songId) {
    // This is implicit - if we're validating within a power hour context, 
    // the entry is connected to that power hour
  } else if (!powerHourId) {
    warnings.push("Cannot verify power hour connection without power hour ID");
  }

  // Check for timing data
  if (entry.idealStartTime === undefined || entry.idealEndTime === undefined) {
    warnings.push("Entry is missing ideal start/end times");
  } else if (entry.idealStartTime >= entry.idealEndTime) {
    warnings.push("Entry has invalid timing (start time >= end time)");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    entryId
  };
}

/**
 * Validates an entire power hour to ensure it has the necessary information to play
 * Requirements:
 * - Power hour must have a valid ID
 * - Power hour must have at least one valid entry
 * - Each entry must pass individual validation
 */
export function validatePowerHour(
  powerHour: PowerHour | null | undefined,
  options: ValidationOptions = {}
): PowerHourValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const missingRequirements: string[] = [];
  const entryValidations: EntryValidationResult[] = [];

  // Set default options
  const validationOptions = {
    requireMinimumEntries: 1,
    allowMissingArtist: false,
    allowMissingTitle: false,
    strictVideoValidation: false,
    ...options
  };

  // Check if power hour exists
  if (!powerHour) {
    errors.push("Power hour is null or undefined");
    missingRequirements.push("Valid power hour object");
    return {
      isValid: false,
      canPlay: false,
      errors,
      warnings,
      missingRequirements,
      entryValidations,
      validEntryCount: 0,
      totalEntryCount: 0
    };
  }

  // Check for power hour ID
  if (!powerHour.id) {
    errors.push("Power hour is missing an ID");
    missingRequirements.push("Power hour ID");
  }

  // Check for entries
  if (!powerHour.entries || !Array.isArray(powerHour.entries)) {
    errors.push("Power hour has no entries array");
    missingRequirements.push("Entries array");
  } else if (powerHour.entries.length === 0) {
    errors.push("Power hour has no entries");
    missingRequirements.push("At least one entry");
  } else {
    // Validate each entry
    powerHour.entries.forEach((entry, index) => {
      const entryValidation = validatePowerHourEntry(entry, powerHour.id, validationOptions);
      entryValidations.push(entryValidation);

      // Add warnings with context (but don't add entry errors to main errors - they're tracked separately)
      entryValidation.warnings.forEach(warning => {
        warnings.push(`Entry ${index + 1} (${entryValidation.entryId}): ${warning}`);
      });
    });

    // Check minimum entry count
    const validEntries = entryValidations.filter(v => v.isValid);
    if (validEntries.length < validationOptions.requireMinimumEntries!) {
      errors.push(`Power hour has only ${validEntries.length} valid entries, but requires at least ${validationOptions.requireMinimumEntries}`);
      missingRequirements.push(`At least ${validationOptions.requireMinimumEntries} valid entries`);
    }
  }

  // Check for basic power hour metadata
  if (!powerHour.title) {
    warnings.push("Power hour is missing a title");
  }

  const validEntryCount = entryValidations.filter(v => v.isValid).length;
  const totalEntryCount = powerHour.entries?.length || 0;
  const isValid = errors.length === 0;
  const canPlay = isValid && validEntryCount > 0;

  return {
    isValid,
    canPlay,
    errors,
    warnings,
    missingRequirements,
    entryValidations,
    validEntryCount,
    totalEntryCount
  };
}

/**
 * Gets a list of valid entries that can be played
 */
export function getValidEntries(powerHour: PowerHour | null | undefined): PowerHourEntry[] {
  if (!powerHour?.entries) {
    return [];
  }

  return powerHour.entries.filter(entry => {
    const validation = validatePowerHourEntry(entry, powerHour.id);
    return validation.isValid;
  });
}

/**
 * Gets a summary of validation issues for display to users
 */
export function getValidationSummary(validation: PowerHourValidationResult): string {
  if (validation.canPlay) {
    if (validation.warnings.length > 0) {
      return `Ready to play with ${validation.validEntryCount} songs (${validation.warnings.length} warnings)`;
    }
    return `Ready to play with ${validation.validEntryCount} songs`;
  }

  if (validation.errors.length > 0) {
    return `Cannot play: ${validation.missingRequirements.join(", ")}`;
  }

  return "Power hour validation failed";
}

/**
 * Quick validation check - returns true if power hour can be played
 */
export function canPlayPowerHour(powerHour: PowerHour | null | undefined): boolean {
  const validation = validatePowerHour(powerHour);
  return validation.canPlay;
}
