import { validatePowerHour, validatePowerHourEntry, canPlayPowerHour, getValidEntries } from '../power-hour-validation';
import { PowerHour, PowerHourEntry } from '../../models/power-hour';

// Mock power hour data for testing
const createMockEntry = (overrides: Partial<PowerHourEntry> = {}): PowerHourEntry => ({
  id: 'entry-1',
  songId: 'song-1',
  videoId: 'dQw4w9WgXcQ',
  song: {
    id: 'song-1',
    title: 'Test Song',
    artist: 'Test Artist',
    genre: 'Pop'
  },
  video: {
    id: 'dQw4w9WgXcQ',
    songId: 'song-1',
    title: 'Test Video',
    description: 'Test video description',
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    thumbnail: 'https://example.com/thumb.jpg',
    provider: 'youtube'
  },
  idealStartTime: 0,
  idealEndTime: 60,
  videoStatus: {
    videoId: 'dQw4w9WgXcQ',
    status: 'Available'
  },
  stats: {},
  ...overrides
});

const createMockPowerHour = (overrides: Partial<PowerHour> = {}): PowerHour => ({
  id: 'ph-1',
  title: 'Test Power Hour',
  search: 'test search',
  description: 'Test description',
  videos: [],
  songs: [],
  entries: [createMockEntry()],
  mostSimilarGenre: 'Pop',
  genres: ['Pop'],
  ...overrides
});

describe('Power Hour Validation', () => {
  describe('validatePowerHourEntry', () => {
    it('should validate a complete entry as valid', () => {
      const entry = createMockEntry();
      const result = validatePowerHourEntry(entry, 'ph-1');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation when entry has no ID', () => {
      const entry = createMockEntry({ id: '' });
      const result = validatePowerHourEntry(entry, 'ph-1');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Entry is missing an ID');
    });

    it('should fail validation when song has no title', () => {
      const entry = createMockEntry({
        song: {
          id: 'song-1',
          title: '',
          artist: 'Test Artist',
          genre: 'Pop'
        }
      });
      const result = validatePowerHourEntry(entry, 'ph-1');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Song is missing a title');
    });

    it('should fail validation when song has no artist', () => {
      const entry = createMockEntry({
        song: {
          id: 'song-1',
          title: 'Test Song',
          artist: '',
          genre: 'Pop'
        }
      });
      const result = validatePowerHourEntry(entry, 'ph-1');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Song is missing an artist');
    });

    it('should fail validation when entry has no video data', () => {
      const entry = createMockEntry({
        videoId: '',
        video: undefined,
        backup_video: undefined
      });
      const result = validatePowerHourEntry(entry, 'ph-1');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Entry is missing video data (no videoId, video.url, or backup_video.link/id found)');
    });

    it('should pass validation with backup_video data', () => {
      const entry = createMockEntry({
        videoId: '',
        video: undefined,
        backup_video: {
          id: 'backup-video-id',
          link: 'https://www.youtube.com/watch?v=backup',
          thumbnail: 'https://example.com/thumb.jpg',
          title: 'Backup Video'
        }
      });
      const result = validatePowerHourEntry(entry, 'ph-1');
      
      expect(result.isValid).toBe(true);
    });
  });

  describe('validatePowerHour', () => {
    it('should validate a complete power hour as valid', () => {
      const powerHour = createMockPowerHour();
      const result = validatePowerHour(powerHour);
      
      expect(result.isValid).toBe(true);
      expect(result.canPlay).toBe(true);
      expect(result.validEntryCount).toBe(1);
      expect(result.totalEntryCount).toBe(1);
    });

    it('should fail validation when power hour is null', () => {
      const result = validatePowerHour(null);
      
      expect(result.isValid).toBe(false);
      expect(result.canPlay).toBe(false);
      expect(result.errors).toContain('Power hour is null or undefined');
    });

    it('should fail validation when power hour has no ID', () => {
      const powerHour = createMockPowerHour({ id: '' });
      const result = validatePowerHour(powerHour);
      
      expect(result.isValid).toBe(false);
      expect(result.canPlay).toBe(false);
      expect(result.errors).toContain('Power hour is missing an ID');
    });

    it('should fail validation when power hour has no entries', () => {
      const powerHour = createMockPowerHour({ entries: [] });
      const result = validatePowerHour(powerHour);
      
      expect(result.isValid).toBe(false);
      expect(result.canPlay).toBe(false);
      expect(result.errors).toContain('Power hour has no entries');
    });

    it('should handle mixed valid and invalid entries', () => {
      const validEntry = createMockEntry();
      const invalidEntry = createMockEntry({
        id: 'entry-2',
        song: {
          id: 'song-2',
          title: '',
          artist: '',
          genre: 'Pop'
        }
      });
      
      const powerHour = createMockPowerHour({
        entries: [validEntry, invalidEntry]
      });
      
      const result = validatePowerHour(powerHour);
      
      expect(result.validEntryCount).toBe(1);
      expect(result.totalEntryCount).toBe(2);
      expect(result.canPlay).toBe(true); // Can still play with at least one valid entry
    });
  });

  describe('canPlayPowerHour', () => {
    it('should return true for a valid power hour', () => {
      const powerHour = createMockPowerHour();
      expect(canPlayPowerHour(powerHour)).toBe(true);
    });

    it('should return false for null power hour', () => {
      expect(canPlayPowerHour(null)).toBe(false);
    });

    it('should return false for power hour with no valid entries', () => {
      const powerHour = createMockPowerHour({ entries: [] });
      expect(canPlayPowerHour(powerHour)).toBe(false);
    });
  });

  describe('getValidEntries', () => {
    it('should return only valid entries', () => {
      const validEntry = createMockEntry();
      const invalidEntry = createMockEntry({
        id: 'entry-2',
        videoId: '',
        video: undefined,
        backup_video: undefined
      });
      
      const powerHour = createMockPowerHour({
        entries: [validEntry, invalidEntry]
      });
      
      const validEntries = getValidEntries(powerHour);
      
      expect(validEntries).toHaveLength(1);
      expect(validEntries[0].id).toBe('entry-1');
    });

    it('should return empty array for null power hour', () => {
      const validEntries = getValidEntries(null);
      expect(validEntries).toHaveLength(0);
    });
  });
});
