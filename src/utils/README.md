# Power Hour Validation System

This validation system ensures that power hours have the necessary information to play correctly. It validates both individual entries and the overall power hour structure.

## Requirements for Playable Power Hours

### Power Hour Level Requirements
- Must have a valid `id`
- Must have at least one valid entry
- Should have a `title` (warning if missing)

### Entry Level Requirements
- Must have an `id`
- Must have song data with:
  - `title` (required)
  - `artist` (required)
- Must have video data from one of:
  - `videoId` field
  - `video.url` field
  - `backup_video.link` or `backup_video.id` field
- Should have timing data (`idealStartTime` and `idealEndTime`)

## Usage

### Basic Validation

```typescript
import { validatePowerHour, canPlayPowerHour } from '@/utils/power-hour-validation';

// Quick check if power hour can be played
const isPlayable = canPlayPowerHour(powerHour);

// Detailed validation with error/warning information
const validation = validatePowerHour(powerHour);
console.log(validation.canPlay); // boolean
console.log(validation.errors); // string[]
console.log(validation.warnings); // string[]
console.log(validation.validEntryCount); // number
```

### Getting Valid Entries

```typescript
import { getValidEntries } from '@/utils/power-hour-validation';

// Get only the entries that can be played
const playableEntries = getValidEntries(powerHour);
```

### Validation Summary

```typescript
import { getValidationSummary } from '@/utils/power-hour-validation';

const validation = validatePowerHour(powerHour);
const summary = getValidationSummary(validation);
// Returns user-friendly messages like:
// "Ready to play with 60 songs"
// "Ready to play with 58 songs (2 warnings)"
// "Cannot play: Power hour ID, At least 1 valid entry"
```

### Using in Components

```typescript
import { PowerHourValidationBadge, PowerHourValidationStatus } from '@/app/components/power-hour-validation/power-hour-validation-status';

// Simple badge
<PowerHourValidationBadge powerHour={powerHour} />

// Detailed status with expandable details
<PowerHourValidationStatus 
  powerHour={powerHour} 
  showDetails={true} 
/>
```

### Provider Functions

The power hour provider also exports validation functions:

```typescript
import { 
  validatePowerHourForPlay, 
  isPowerHourPlayable, 
  getPowerHourValidationSummary 
} from '@/app/providers/power-hour';

const isPlayable = isPowerHourPlayable(powerHour);
const summary = getPowerHourValidationSummary(powerHour);
```

## Validation Options

You can customize validation behavior:

```typescript
const validation = validatePowerHour(powerHour, {
  requireMinimumEntries: 5, // Require at least 5 valid entries
  allowMissingArtist: true, // Allow songs without artist
  allowMissingTitle: false, // Require song titles
  strictVideoValidation: true // Prefer direct video IDs over URLs
});
```

## Integration Points

The validation system is integrated into:

1. **Power Hour Creation** - Validates before marking as complete
2. **Active Power Hour** - Validates before allowing playback
3. **Power Hour Cards** - Shows validation status badges
4. **Power Hour Provider** - Provides validation utilities

## Error Types

### Critical Errors (Prevent Playback)
- Missing power hour ID
- No entries array
- No valid entries
- Entry missing ID
- Entry missing song data
- Entry missing video data

### Warnings (Allow Playback)
- Missing song title/artist (when allowed)
- Missing timing data
- Invalid timing ranges
- Missing power hour title

## Testing

Run the validation tests:

```bash
npm test src/utils/__tests__/power-hour-validation.test.ts
```

The test suite covers:
- Individual entry validation
- Power hour validation
- Mixed valid/invalid entries
- Edge cases (null/undefined data)
- Validation options
