export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  validEntryCount: number;
  totalEntryCount: number;
}

export interface EntryValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  entryId: string;
}

export interface PowerHourValidationResult extends ValidationResult {
  canPlay: boolean;
  missingRequirements: string[];
  entryValidations: EntryValidationResult[];
}

export interface ValidationOptions {
  requireMinimumEntries?: number;
  allowMissingArtist?: boolean;
  allowMissingTitle?: boolean;
  strictVideoValidation?: boolean;
}
